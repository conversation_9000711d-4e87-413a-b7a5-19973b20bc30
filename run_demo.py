#!/usr/bin/env python3
"""
启动脚本 - Deep Research Assistant Demo
"""

import subprocess
import sys
import os
from pathlib import Path

def main():
    """启动演示应用"""
    print("🔍 Deep Research Assistant - Demo")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = Path.cwd()
    print(f"当前目录: {current_dir}")
    
    # 检查必要文件
    demo_file = current_dir / "demo_app.py"
    if not demo_file.exists():
        print("❌ 错误: demo_app.py 文件不存在")
        return 1
    
    print("✅ 找到演示应用文件")
    
    # 检查 streamlit 是否安装
    try:
        import streamlit
        print(f"✅ Streamlit 版本: {streamlit.__version__}")
    except ImportError:
        print("❌ 错误: Streamlit 未安装")
        print("请运行: uv add streamlit")
        return 1
    
    # 启动应用
    print("\n🚀 启动演示应用...")
    print("应用将在浏览器中打开")
    print("按 Ctrl+C 停止应用")
    print("-" * 50)
    
    try:
        # 使用 subprocess 启动 streamlit
        cmd = [sys.executable, "-m", "streamlit", "run", "demo_app.py"]
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return 1
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

# API Keys for Deep Research App
# Copy this file to .env and fill in your actual API keys

# OpenAI API Key (required for LLM functionality)
# 如果使用第三方OpenAI兼容API，这里填入第三方提供的API密钥
OPENAI_API_KEY=your_openai_api_key_here

# Tavily API Key (required for web search functionality)
TAVILY_API_KEY=your_tavily_api_key_here

# Model Configuration (模型配置)
# 模型提供商 (openai, anthropic, google, etc.)
MODEL_PROVIDER=openai

# 模型名称 (建议使用更强大的模型以获得更好的分析质量)
MODEL_NAME=gpt-4o-mini

# 第三方API的Base URL (如果使用第三方OpenAI兼容API)
# 例如: https://api.deepseek.com/v1 或 https://api.moonshot.cn/v1
# 如果使用官方OpenAI API，请注释掉这一行
# MODEL_BASE_URL=https://your-third-party-api.com/v1

# 模型参数优化 (可选)
# 温度设置 - 控制输出的创造性 (0.0-1.0)
MODEL_TEMPERATURE=0.1

# 最大输出长度 - 控制生成内容的长度
MODEL_MAX_TOKENS=4000

# 搜索结果数量 - 控制每次搜索返回的结果数
SEARCH_MAX_RESULTS=5

# 常见第三方API配置示例:
# DeepSeek API
# MODEL_BASE_URL=https://api.deepseek.com/v1
# MODEL_NAME=deepseek-chat

# Moonshot API
# MODEL_BASE_URL=https://api.moonshot.cn/v1
# MODEL_NAME=moonshot-v1-8k

# 智谱AI API
# MODEL_BASE_URL=https://open.bigmodel.cn/api/paas/v4
# MODEL_NAME=glm-4

# Optional: LangSmith for tracing (uncomment if you want to use LangSmith)
# LANGCHAIN_TRACING_V2=true
# LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
# LANGCHAIN_API_KEY=your_langsmith_api_key_here
# LANGCHAIN_PROJECT=deep-research-app

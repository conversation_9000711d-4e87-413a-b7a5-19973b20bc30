# Deep Research Assistant

基于 LangGraph 和 Streamlit 构建的智能深度研究助手，能够自动执行复杂的研究任务，包括信息搜索、分析和报告生成。

## 功能特性

- 🔍 **智能研究规划**: 基于研究主题自动生成详细的研究计划
- 🌐 **网络信息搜索**: 使用 Tavily 搜索引擎获取最新信息
- 📊 **深度分析**: AI 驱动的信息分析和综合
- 📄 **自动报告生成**: 生成结构化的研究报告
- 💾 **状态管理**: 使用 LangGraph 的检查点功能保存研究进度
- 🎨 **直观界面**: Streamlit 构建的用户友好界面

## 技术架构

### 核心组件

1. **LangGraph 工作流**
   - 研究规划节点 (Planner)
   - 信息搜索节点 (Searcher)
   - 分析综合节点 (Analyzer)
   - 报告生成节点 (Reporter)

2. **Streamlit 界面**
   - 配置管理
   - 实时状态显示
   - 结果展示
   - 报告下载

3. **AI 模型集成**
   - OpenAI GPT-4o-mini 用于文本生成和分析
   - Tavily 搜索引擎用于信息检索

## 安装和设置

### 1. 环境要求

- Python 3.11+
- uv (推荐) 或 pip

### 2. 安装依赖

使用 uv (推荐):
```bash
uv add langgraph streamlit langchain-openai langchain-community langchain-core tavily-python python-dotenv
```

或使用 pip:
```bash
pip install langgraph streamlit langchain-openai langchain-community langchain-core tavily-python python-dotenv
```

### 3. 配置 API 密钥

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，填入你的 API 密钥：
```env
OPENAI_API_KEY=your_openai_api_key_here
TAVILY_API_KEY=your_tavily_api_key_here
```

### 4. 获取 API 密钥

- **OpenAI API Key**: 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
- **Tavily API Key**: 访问 [Tavily](https://tavily.com/) 注册获取

## 使用方法

### 启动应用

```bash
streamlit run deep_research_app.py
```

### 使用步骤

1. **配置 API 密钥**: 在侧边栏输入你的 OpenAI 和 Tavily API 密钥
2. **输入研究主题**: 在主界面输入你想要研究的主题
3. **开始研究**: 点击"开始深度研究"按钮
4. **查看进度**: 在侧边栏监控研究进度
5. **查看结果**: 在不同标签页查看研究计划、搜索结果、分析报告和最终报告
6. **下载报告**: 在最终报告页面下载 Markdown 格式的报告

### 示例研究主题

- 人工智能在医疗诊断中的应用现状和发展趋势
- 区块链技术在供应链管理中的应用
- 可再生能源的最新发展趋势
- 远程工作对企业文化的影响
- 量子计算在密码学中的应用前景

## 项目结构

```
langgraph_augment_1/
├── deep_research_app.py    # 主应用文件
├── .env.example           # 环境变量模板
├── README.md             # 项目说明
├── pyproject.toml        # 项目配置
└── main.py              # 原始入口文件
```

## 工作流程详解

### 1. 研究规划 (Planning)
- 分析研究主题
- 生成详细的研究计划
- 确定关键研究方向

### 2. 信息搜索 (Searching)
- 基于研究计划生成搜索查询
- 使用 Tavily 搜索引擎获取信息
- 收集多个来源的数据

### 3. 分析综合 (Analyzing)
- 分析搜索结果
- 识别关键发现和趋势
- 进行对比分析

### 4. 报告生成 (Reporting)
- 整合所有研究结果
- 生成结构化报告
- 提供结论和建议

## 自定义和扩展

### 添加新的搜索工具
```python
def create_research_tools():
    tools = [
        TavilySearchResults(max_results=5),
        # 添加其他搜索工具
    ]
    return tools
```

### 修改研究流程
```python
def create_research_graph():
    workflow = StateGraph(ResearchState)
    # 添加自定义节点
    workflow.add_node("custom_node", custom_function)
    # 修改流程
    return workflow.compile()
```

## 故障排除

### 常见问题

1. **API 密钥错误**: 确保 API 密钥正确且有效
2. **网络连接问题**: 检查网络连接和防火墙设置
3. **依赖版本冲突**: 使用虚拟环境隔离依赖

### 调试模式

设置环境变量启用详细日志：
```bash
export LANGCHAIN_VERBOSE=true
export LANGCHAIN_TRACING_V2=true
```

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License

## 更新日志

### v1.0.0
- 初始版本发布
- 基本的研究工作流
- Streamlit 用户界面
- 支持 OpenAI 和 Tavily 集成
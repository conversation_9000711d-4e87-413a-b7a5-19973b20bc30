# Deep Research Assistant - 使用指南

## 项目概述

这个项目包含两个版本的深度研究助手：

1. **完整版本** (`deep_research_app.py`) - 使用真实的 LangGraph 和 API 集成
2. **演示版本** (`demo_app.py`) - 无需 API 密钥的模拟版本

## 快速开始

### 方式一：运行演示版本（推荐新手）

演示版本无需任何 API 密钥，可以立即体验完整的研究流程：

```bash
# 启动演示应用
streamlit run demo_app.py

# 或使用启动脚本
python run_demo.py
```

**演示版本特点：**
- ✅ 无需 API 密钥
- ✅ 模拟完整研究流程
- ✅ 生成示例报告
- ✅ 展示所有界面功能

### 方式二：运行完整版本（需要 API 密钥）

完整版本使用真实的 AI 模型和搜索引擎：

```bash
# 1. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入真实的 API 密钥

# 2. 启动完整应用
streamlit run deep_research_app.py
```

**完整版本特点：**
- 🔑 需要 OpenAI 和 Tavily API 密钥
- 🌐 真实的网络搜索
- 🤖 AI 驱动的分析
- 📊 动态生成的内容

## 功能演示

### 1. 研究主题输入
在主界面输入您想要研究的主题，例如：
- "人工智能在医疗诊断中的应用"
- "区块链技术的发展趋势"
- "可再生能源的未来前景"

### 2. 自动化研究流程
系统会自动执行以下步骤：
1. **规划阶段** - 生成详细的研究计划
2. **搜索阶段** - 收集相关信息（完整版）或模拟数据（演示版）
3. **分析阶段** - 深度分析收集的信息
4. **报告阶段** - 生成结构化的研究报告

### 3. 结果查看
研究完成后，可以在不同标签页查看：
- 📋 **研究计划** - 详细的研究步骤
- 🔍 **搜索结果** - 收集的信息（仅完整版）
- 📈 **分析报告** - AI 生成的分析
- 📄 **最终报告** - 完整的研究报告

### 4. 报告下载
最终报告可以下载为 Markdown 格式文件，便于后续使用。

## 技术架构说明

### LangGraph 工作流
```
开始 → 研究规划 → 信息搜索 → 分析综合 → 报告生成 → 结束
```

### 核心组件
- **StateGraph**: 管理研究流程状态
- **节点函数**: 执行具体的研究任务
- **状态管理**: 在不同步骤间传递数据
- **检查点**: 保存研究进度（完整版）

### Streamlit 界面
- **响应式布局**: 适配不同屏幕尺寸
- **实时状态**: 显示当前研究进度
- **标签页**: 组织不同类型的结果
- **交互控件**: 用户输入和配置

## API 密钥获取

### OpenAI API Key
1. 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
2. 注册或登录账户
3. 创建新的 API 密钥
4. 复制密钥到 `.env` 文件

### Tavily API Key
1. 访问 [Tavily](https://tavily.com/)
2. 注册账户
3. 获取 API 密钥
4. 复制密钥到 `.env` 文件

## 自定义和扩展

### 添加新的研究节点
```python
def custom_research_node(state: ResearchState):
    # 自定义研究逻辑
    return {"custom_field": "custom_value"}

# 添加到工作流
workflow.add_node("custom_node", custom_research_node)
```

### 修改界面布局
```python
# 在 main() 函数中修改布局
col1, col2, col3 = st.columns([1, 2, 1])
```

### 集成其他工具
```python
# 添加新的搜索工具
from langchain_community.tools import WikipediaQueryRun

def create_research_tools():
    return [
        TavilySearchResults(max_results=5),
        WikipediaQueryRun(),
        # 其他工具...
    ]
```

## 故障排除

### 常见问题

**Q: 演示版本无法启动**
A: 确保已安装 streamlit：`uv add streamlit`

**Q: 完整版本 API 错误**
A: 检查 API 密钥是否正确，账户是否有余额

**Q: 搜索功能不工作**
A: 检查网络连接和 Tavily API 密钥

**Q: 界面显示异常**
A: 尝试刷新浏览器或重启应用

### 调试模式
```bash
# 启用详细日志
export STREAMLIT_LOGGER_LEVEL=debug
streamlit run demo_app.py
```

## 项目文件说明

```
langgraph_augment_1/
├── deep_research_app.py    # 完整版应用（需要API密钥）
├── demo_app.py            # 演示版应用（无需API密钥）
├── run_demo.py           # 启动脚本
├── .env.example          # 环境变量模板
├── README.md            # 项目说明
├── USAGE_GUIDE.md       # 使用指南（本文件）
├── pyproject.toml       # 项目配置
└── main.py             # 原始入口文件
```

## 下一步计划

### 功能增强
- [ ] 添加更多搜索引擎支持
- [ ] 支持文档上传和分析
- [ ] 添加图表和可视化
- [ ] 支持多语言研究

### 界面改进
- [ ] 添加主题切换
- [ ] 优化移动端显示
- [ ] 添加进度动画
- [ ] 支持研究历史记录

### 技术优化
- [ ] 添加缓存机制
- [ ] 优化性能
- [ ] 添加错误恢复
- [ ] 支持并发研究

## 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License - 详见 LICENSE 文件

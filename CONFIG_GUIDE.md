# 配置指南 - Deep Research Assistant

## 环境配置

### 1. 基本设置

复制环境变量模板：
```bash
cp .env.example .env
```

### 2. API 密钥配置

#### 必需的 API 密钥

**OpenAI API Key (或兼容的第三方API密钥)**
```env
OPENAI_API_KEY=your_api_key_here
```

**Tavily API Key (用于网络搜索)**
```env
TAVILY_API_KEY=your_tavily_api_key_here
```

### 3. 模型配置

#### 使用官方 OpenAI API
```env
MODEL_PROVIDER=openai
MODEL_NAME=gpt-4o-mini
# MODEL_BASE_URL 留空或注释掉
```

#### 使用第三方 OpenAI 兼容 API

**DeepSeek API**
```env
MODEL_PROVIDER=openai
MODEL_NAME=deepseek-chat
MODEL_BASE_URL=https://api.deepseek.com/v1
OPENAI_API_KEY=your_deepseek_api_key
```

**Moonshot API (月之暗面)**
```env
MODEL_PROVIDER=openai
MODEL_NAME=moonshot-v1-8k
MODEL_BASE_URL=https://api.moonshot.cn/v1
OPENAI_API_KEY=your_moonshot_api_key
```

**智谱AI (GLM)**
```env
MODEL_PROVIDER=openai
MODEL_NAME=glm-4
MODEL_BASE_URL=https://open.bigmodel.cn/api/paas/v4
OPENAI_API_KEY=your_zhipu_api_key
```

**通义千问 (Qwen)**
```env
MODEL_PROVIDER=openai
MODEL_NAME=qwen-turbo
MODEL_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
OPENAI_API_KEY=your_dashscope_api_key
```

**百川 AI**
```env
MODEL_PROVIDER=openai
MODEL_NAME=Baichuan2-Turbo
MODEL_BASE_URL=https://api.baichuan-ai.com/v1
OPENAI_API_KEY=your_baichuan_api_key
```

## API 密钥获取指南

### OpenAI 官方 API
1. 访问 [OpenAI Platform](https://platform.openai.com/api-keys)
2. 注册或登录账户
3. 创建新的 API 密钥
4. 复制密钥到 `.env` 文件

### 第三方 API 提供商

#### DeepSeek
1. 访问 [DeepSeek 开放平台](https://platform.deepseek.com/)
2. 注册账户并完成认证
3. 在控制台创建 API 密钥
4. 获取密钥并配置

#### Moonshot (月之暗面)
1. 访问 [Moonshot AI](https://platform.moonshot.cn/)
2. 注册账户
3. 在 API 管理页面创建密钥
4. 配置到环境变量

#### 智谱AI
1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册并实名认证
3. 创建 API Key
4. 配置到环境变量

#### 通义千问
1. 访问 [阿里云DashScope](https://dashscope.aliyun.com/)
2. 开通服务并获取 API Key
3. 配置到环境变量

### Tavily 搜索 API
1. 访问 [Tavily](https://tavily.com/)
2. 注册账户
3. 获取 API 密钥
4. 配置到环境变量

## 配置验证

启动应用后，侧边栏会显示配置状态：
- ✅ 绿色表示配置正确
- ❌ 红色表示缺少必要配置

## 成本优化建议

### 选择合适的模型
- **开发测试**: 使用较小的模型如 `gpt-4o-mini`
- **生产环境**: 根据需求选择合适的模型

### 第三方 API 优势
- **成本更低**: 通常比官方 API 便宜
- **国内访问**: 无需科学上网
- **多样选择**: 不同模型有不同特点

### 推荐配置

**预算有限**:
```env
MODEL_PROVIDER=openai
MODEL_NAME=deepseek-chat
MODEL_BASE_URL=https://api.deepseek.com/v1
```

**平衡性能和成本**:
```env
MODEL_PROVIDER=openai
MODEL_NAME=moonshot-v1-8k
MODEL_BASE_URL=https://api.moonshot.cn/v1
```

**追求性能**:
```env
MODEL_PROVIDER=openai
MODEL_NAME=gpt-4o-mini
# 使用官方 OpenAI API
```

## 故障排除

### 常见问题

**Q: API 密钥无效**
A: 检查密钥是否正确，账户是否有余额

**Q: 网络连接失败**
A: 检查 BASE_URL 是否正确，网络是否正常

**Q: 模型不支持**
A: 确认模型名称是否正确，是否在支持列表中

**Q: 搜索功能不工作**
A: 检查 Tavily API 密钥是否配置正确

### 调试模式

启用详细日志：
```env
LANGCHAIN_VERBOSE=true
LANGCHAIN_TRACING_V2=true
```

## 安全建议

1. **不要提交 .env 文件到版本控制**
2. **定期轮换 API 密钥**
3. **设置 API 使用限额**
4. **监控 API 使用情况**

## 更新配置

修改 `.env` 文件后，重启应用即可生效：
```bash
# 停止应用 (Ctrl+C)
# 重新启动
streamlit run deep_research_app.py
```

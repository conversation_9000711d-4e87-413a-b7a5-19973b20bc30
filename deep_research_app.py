import streamlit as st
import os
from typing import TypedDict, Annotated, List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_community.tools.tavily_search import TavilySearchResults
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, tools_condition
from langgraph.checkpoint.memory import MemorySaver
import json
from datetime import datetime
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置检查和初始化
def check_api_keys():
    """检查必要的API密钥是否已配置"""
    openai_key = os.getenv("OPENAI_API_KEY")
    tavily_key = os.getenv("TAVILY_API_KEY")

    missing_keys = []
    if not openai_key:
        missing_keys.append("OPENAI_API_KEY")
    if not tavily_key:
        missing_keys.append("TAVILY_API_KEY")

    return missing_keys

# 创建LLM实例（支持第三方模型）
def create_llm():
    """创建LLM实例，支持多种模型提供商"""
    # 从环境变量获取模型配置
    model_provider = os.getenv("MODEL_PROVIDER", "openai")  # 默认使用OpenAI
    model_name = os.getenv("MODEL_NAME", "gpt-4o-mini")
    base_url = os.getenv("MODEL_BASE_URL")  # 第三方API的base_url
    api_key = os.getenv("OPENAI_API_KEY")  # 可以复用这个环境变量名

    if model_provider.lower() == "openai":
        if base_url:
            # 使用第三方OpenAI兼容API
            return ChatOpenAI(
                model=model_name,
                api_key=api_key,
                base_url=base_url,
                temperature=0.1
            )
        else:
            # 使用官方OpenAI API
            return ChatOpenAI(
                model=model_name,
                api_key=api_key,
                temperature=0.1
            )
    else:
        # 可以在这里添加其他模型提供商的支持
        # 例如：Anthropic, Google, etc.
        return ChatOpenAI(
            model=model_name,
            api_key=api_key,
            base_url=base_url,
            temperature=0.1
        )

# 页面配置
st.set_page_config(
    page_title="Deep Research Assistant",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 定义状态结构
class ResearchState(TypedDict):
    messages: Annotated[List, add_messages]
    research_topic: str
    research_plan: List[str]
    search_results: List[Dict[str, Any]]
    analysis_results: List[str]
    final_report: str
    current_step: str

# 初始化会话状态
def initialize_session_state():
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "research_state" not in st.session_state:
        st.session_state.research_state = {
            "messages": [],
            "research_topic": "",
            "research_plan": [],
            "search_results": [],
            "analysis_results": [],
            "final_report": "",
            "current_step": "planning"
        }
    if "research_graph" not in st.session_state:
        st.session_state.research_graph = None

# 创建研究工具
def create_research_tools():
    """创建研究所需的工具"""
    search_tool = TavilySearchResults(
        max_results=5,
        search_depth="advanced",
        include_answer=True,
        include_raw_content=True
    )
    return [search_tool]

# 研究规划节点
def research_planner(state: ResearchState):
    """制定研究计划"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月")

    system_prompt = f"""你是一个专业的研究规划师。根据用户提供的研究主题，制定一个详细的研究计划。

重要提醒：
- 当前时间是{current_date}
- 如果研究主题涉及"今年"、"最新"、"当前"等时间概念，请明确指向{current_year}年
- 研究计划应该重点关注{current_year}年的最新信息和趋势

请按以下格式输出研究计划：
1. 确定研究范围和关键问题（明确时间范围为{current_year}年）
2. 识别需要搜索的关键词和概念（包含{current_year}年相关关键词）
3. 规划信息收集的步骤（重点收集{current_year}年的最新数据）
4. 确定分析和综合的方法

研究计划应该具体、可执行，并且能够全面覆盖研究主题的最新发展。"""

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=f"请为以下研究主题制定详细的研究计划：{state['research_topic']}")
    ]

    response = llm.invoke(messages)

    # 解析研究计划
    plan_lines = response.content.split('\n')
    research_plan = [line.strip() for line in plan_lines if line.strip() and not line.strip().startswith('#')]

    return {
        "messages": [response],
        "research_plan": research_plan,
        "current_step": "searching"
    }

# 信息搜索节点
def information_searcher(state: ResearchState):
    """执行信息搜索"""
    try:
        search_tool = TavilySearchResults(
            max_results=3,
            search_depth="advanced",
            include_answer=True
        )

        # 获取当前年份和月份
        current_year = datetime.now().year
        current_month = datetime.now().strftime("%Y年%m月")

        # 基于研究计划生成搜索查询
        topic = state['research_topic']

        # 生成包含时间信息的搜索查询
        base_queries = [
            f"{topic} {current_year}年最新发展",
            f"{topic} {current_year}年趋势分析",
            f"{topic} {current_month}最新资讯"
        ]

        # 如果主题包含"今年"、"最新"等词汇，添加更具体的时间查询
        if any(keyword in topic for keyword in ["今年", "最新", "当前", "现在"]):
            base_queries.extend([
                f"{topic.replace('今年', str(current_year))} {current_year}",
                f"{topic} {current_year}年数据",
                f"{topic} {current_year}年报告"
            ])

        search_results = []
        for query in base_queries[:4]:  # 限制搜索次数
            try:
                # 直接使用搜索工具
                results = search_tool.invoke({"query": query})
                search_results.append({
                    "query": query,
                    "response": str(results),
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                search_results.append({
                    "query": query,
                    "response": f"搜索失败: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                })

        return {
            "messages": [AIMessage(content=f"完成了 {len(search_results)} 个搜索查询")],
            "search_results": search_results,
            "current_step": "analyzing"
        }
    except Exception as e:
        return {
            "messages": [AIMessage(content=f"搜索过程出现错误: {str(e)}")],
            "search_results": [],
            "current_step": "analyzing"
        }

# 分析综合节点
def research_analyzer(state: ResearchState):
    """分析和综合研究结果"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月%d日")

    # 准备分析内容
    search_content = "\n\n".join([
        f"搜索查询: {result['query']}\n结果: {result['response']}"
        for result in state['search_results']
    ])

    analysis_prompt = f"""作为专业研究分析师，请分析以下搜索结果并提供深入见解：

研究主题: {state['research_topic']}
分析时间: {current_date}

重要提醒：
- 当前是{current_year}年，请重点关注{current_year}年的最新数据和趋势
- 如果研究主题涉及"今年"等时间概念，请明确指向{current_year}年
- 优先分析最新的、时效性强的信息

搜索结果:
{search_content}

请提供：
1. {current_year}年的关键发现和最新趋势
2. 不同来源信息的对比分析（重点关注时效性）
3. {current_year}年的新发展和变化
4. 实际应用和当前影响
5. 基于{current_year}年数据的未来预测和建议

请确保分析客观、全面且具有时效性，重点突出{current_year}年的最新情况。"""

    response = llm.invoke([HumanMessage(content=analysis_prompt)])

    return {
        "messages": [response],
        "analysis_results": [response.content],
        "current_step": "reporting"
    }

# 报告生成节点
def report_generator(state: ResearchState):
    """生成最终研究报告"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月%d日")

    # 整合所有信息
    all_content = {
        "topic": state['research_topic'],
        "plan": state['research_plan'],
        "search_results": state['search_results'],
        "analysis": state['analysis_results']
    }

    report_prompt = f"""作为专业报告撰写者，请基于以下研究内容生成一份完整的研究报告：

研究主题: {all_content['topic']}
报告生成时间: {current_date}

重要提醒：
- 当前是{current_year}年，报告应重点关注{current_year}年的最新情况
- 如果研究主题涉及"今年"等时间概念，请明确指向{current_year}年
- 确保所有数据和分析都基于最新的时间范围

研究计划:
{chr(10).join(all_content['plan'])}

分析结果:
{chr(10).join(all_content['analysis'])}

请按以下结构生成报告：
# {all_content['topic']} - {current_year}年深度研究报告

## 执行摘要
（重点突出{current_year}年的关键发现）

## 研究背景
（说明研究的时间范围为{current_year}年）

## {current_year}年主要发现
（基于最新数据的核心发现）

## 详细分析
（深入分析{current_year}年的趋势和变化）

## 结论和建议
（基于{current_year}年情况的前瞻性建议）

## 参考信息
（注明数据截止时间为{current_date}）

报告应该专业、结构清晰、内容丰富且具有时效性，确保读者了解这是基于{current_year}年最新信息的分析。"""

    response = llm.invoke([HumanMessage(content=report_prompt)])

    return {
        "messages": [response],
        "final_report": response.content,
        "current_step": "completed"
    }

# 创建研究图
def create_research_graph():
    """创建LangGraph研究工作流"""
    workflow = StateGraph(ResearchState)

    # 添加节点
    workflow.add_node("planner", research_planner)
    workflow.add_node("searcher", information_searcher)
    workflow.add_node("analyzer", research_analyzer)
    workflow.add_node("reporter", report_generator)

    # 定义简化的流程
    workflow.add_edge(START, "planner")
    workflow.add_edge("planner", "searcher")
    workflow.add_edge("searcher", "analyzer")
    workflow.add_edge("analyzer", "reporter")
    workflow.add_edge("reporter", END)

    # 添加检查点
    memory = MemorySaver()
    graph = workflow.compile(checkpointer=memory)

    return graph

# 主应用界面
def main():
    st.title("🔍 Deep Research Assistant")
    st.markdown("基于 LangGraph 和 Streamlit 的智能深度研究助手")
    
    # 初始化
    initialize_session_state()
    
    # 检查API密钥配置
    missing_keys = check_api_keys()

    # 侧边栏配置
    with st.sidebar:
        st.header("🛠️ 配置状态")

        # 显示API密钥状态
        if missing_keys:
            st.error("❌ 缺少必要的API密钥")
            for key in missing_keys:
                st.write(f"- {key}")
            st.info("请在 .env 文件中配置API密钥")
        else:
            st.success("✅ API密钥配置完成")

        # 显示模型配置
        st.subheader("🤖 模型配置")
        model_provider = os.getenv("MODEL_PROVIDER", "openai")
        model_name = os.getenv("MODEL_NAME", "gpt-4o-mini")
        base_url = os.getenv("MODEL_BASE_URL", "默认")

        st.write(f"**提供商**: {model_provider}")
        st.write(f"**模型**: {model_name}")
        st.write(f"**API地址**: {base_url}")

        st.divider()

        # 研究状态显示
        st.header("📊 研究状态")
        current_step = st.session_state.research_state.get("current_step", "planning")

        steps = ["planning", "searching", "analyzing", "reporting", "completed"]
        step_names = ["规划", "搜索", "分析", "报告", "完成"]

        for i, (step, name) in enumerate(zip(steps, step_names)):
            if step == current_step:
                st.success(f"✅ {name}")
            elif steps.index(current_step) > i:
                st.success(f"✅ {name}")
            else:
                st.info(f"⏳ {name}")
    
    # 主界面
    col1, col2 = st.columns([2, 1])

    with col1:
        st.header("🎯 研究主题")
        research_topic = st.text_area(
            "请输入您想要深入研究的主题：",
            height=100,
            placeholder="例如：人工智能在医疗诊断中的应用现状和发展趋势"
        )

        # 检查是否可以开始研究
        can_start_research = len(missing_keys) == 0

        if st.button("🚀 开始深度研究", type="primary", disabled=not can_start_research):
            if not can_start_research:
                st.error("请先在 .env 文件中配置必要的API密钥")
            elif research_topic:
                with st.spinner("正在初始化研究工作流..."):
                    # 创建研究图
                    st.session_state.research_graph = create_research_graph()

                    # 更新研究状态
                    st.session_state.research_state["research_topic"] = research_topic
                    st.session_state.research_state["current_step"] = "planning"

                    st.success("研究工作流已启动！")
                    st.rerun()
            else:
                st.warning("请输入研究主题")

    with col2:
        st.header("📋 快速开始")
        st.markdown("""
        **使用步骤：**
        1. 在侧边栏输入API密钥
        2. 输入研究主题
        3. 点击开始研究
        4. 等待AI完成分析
        5. 查看详细报告

        **示例主题：**
        - 区块链技术在供应链管理中的应用
        - 可再生能源的最新发展趋势
        - 远程工作对企业文化的影响
        """)

    # 执行研究工作流
    if st.session_state.research_graph and st.session_state.research_state["research_topic"]:
        execute_research_workflow()

    # 显示研究结果
    display_research_results()

def execute_research_workflow():
    """执行研究工作流"""
    if st.session_state.research_state["current_step"] != "completed":
        with st.spinner("🔍 正在执行深度研究..."):
            try:
                # 准备初始状态
                initial_state = {
                    "messages": [],
                    "research_topic": st.session_state.research_state["research_topic"],
                    "research_plan": [],
                    "search_results": [],
                    "analysis_results": [],
                    "final_report": "",
                    "current_step": "planning"
                }

                # 执行工作流
                config = {"configurable": {"thread_id": "research_thread"}}
                final_state = st.session_state.research_graph.invoke(initial_state, config)

                # 更新会话状态
                st.session_state.research_state.update(final_state)

                st.success("✅ 研究完成！")
                st.rerun()

            except Exception as e:
                st.error(f"研究过程中出现错误：{str(e)}")

def display_research_results():
    """显示研究结果"""
    if st.session_state.research_state["research_topic"]:
        st.header("📊 研究结果")

        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["📋 研究计划", "🔍 搜索结果", "📈 分析报告", "📄 最终报告"])

        with tab1:
            st.subheader("研究计划")
            if st.session_state.research_state["research_plan"]:
                for i, plan_item in enumerate(st.session_state.research_state["research_plan"], 1):
                    st.write(f"{i}. {plan_item}")
            else:
                st.info("研究计划生成中...")

        with tab2:
            st.subheader("搜索结果")
            if st.session_state.research_state["search_results"]:
                for i, result in enumerate(st.session_state.research_state["search_results"], 1):
                    with st.expander(f"搜索 {i}: {result['query']}"):
                        st.write("**搜索结果:**")
                        st.write(result['response'])
                        st.caption(f"搜索时间: {result['timestamp']}")
            else:
                st.info("搜索结果生成中...")

        with tab3:
            st.subheader("分析报告")
            if st.session_state.research_state["analysis_results"]:
                for analysis in st.session_state.research_state["analysis_results"]:
                    st.markdown(analysis)
            else:
                st.info("分析报告生成中...")

        with tab4:
            st.subheader("最终报告")
            if st.session_state.research_state["final_report"]:
                st.markdown(st.session_state.research_state["final_report"])

                # 添加下载按钮
                st.download_button(
                    label="📥 下载报告",
                    data=st.session_state.research_state["final_report"],
                    file_name=f"research_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                    mime="text/markdown"
                )
            else:
                st.info("最终报告生成中...")

if __name__ == "__main__":
    main()

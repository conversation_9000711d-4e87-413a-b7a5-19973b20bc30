import streamlit as st
import os
from typing import TypedDict, Annotated, List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_community.tools.tavily_search import TavilySearchResults
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, tools_condition
from langgraph.checkpoint.memory import MemorySaver
import json
from datetime import datetime
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置检查和初始化
def check_api_keys():
    """检查必要的API密钥是否已配置"""
    openai_key = os.getenv("OPENAI_API_KEY")
    tavily_key = os.getenv("TAVILY_API_KEY")

    missing_keys = []
    if not openai_key:
        missing_keys.append("OPENAI_API_KEY")
    if not tavily_key:
        missing_keys.append("TAVILY_API_KEY")

    return missing_keys

# 创建LLM实例（支持第三方模型）
def create_llm():
    """创建LLM实例，支持多种模型提供商和参数配置"""
    # 从环境变量获取模型配置
    model_provider = os.getenv("MODEL_PROVIDER", "openai")
    model_name = os.getenv("MODEL_NAME", "gpt-4o-mini")
    base_url = os.getenv("MODEL_BASE_URL")
    api_key = os.getenv("OPENAI_API_KEY")

    # 获取模型参数配置
    temperature = float(os.getenv("MODEL_TEMPERATURE", "0.1"))
    max_tokens = int(os.getenv("MODEL_MAX_TOKENS", "4000"))

    if model_provider.lower() == "openai":
        if base_url:
            # 使用第三方OpenAI兼容API
            return ChatOpenAI(
                model=model_name,
                api_key=api_key,
                base_url=base_url,
                temperature=temperature,
                max_tokens=max_tokens
            )
        else:
            # 使用官方OpenAI API
            return ChatOpenAI(
                model=model_name,
                api_key=api_key,
                temperature=temperature,
                max_tokens=max_tokens
            )
    else:
        # 可以在这里添加其他模型提供商的支持
        # 例如：Anthropic, Google, etc.
        return ChatOpenAI(
            model=model_name,
            api_key=api_key,
            base_url=base_url,
            temperature=temperature,
            max_tokens=max_tokens
        )

# 页面配置
st.set_page_config(
    page_title="Deep Research Assistant",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 定义状态结构
class ResearchState(TypedDict):
    messages: Annotated[List, add_messages]
    research_topic: str
    user_intent: Dict[str, Any]  # 用户意图分析结果
    research_plan: List[str]
    search_queries: List[str]  # 生成的搜索查询
    search_results: List[Dict[str, Any]]
    analysis_results: List[str]
    final_report: str
    current_step: str

# 初始化会话状态
def initialize_session_state():
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "research_state" not in st.session_state:
        st.session_state.research_state = {
            "messages": [],
            "research_topic": "",
            "user_intent": {},
            "research_plan": [],
            "search_queries": [],
            "search_results": [],
            "analysis_results": [],
            "final_report": "",
            "current_step": "intent_analysis"
        }
    if "research_graph" not in st.session_state:
        st.session_state.research_graph = None

# 创建研究工具
def create_research_tools():
    """创建研究所需的工具"""
    # 从环境变量获取搜索配置
    max_results = int(os.getenv("SEARCH_MAX_RESULTS", "5"))

    search_tool = TavilySearchResults(
        max_results=max_results,
        search_depth="advanced",
        include_answer=True,
        include_raw_content=True
    )
    return [search_tool]

# 用户意图分析节点
def intent_analyzer(state: ResearchState):
    """分析用户意图，确定研究方向和策略"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月")

    intent_prompt = f"""你是一位专业的用户意图分析师。请深入分析用户的研究需求，识别其真实意图和期望。

当前时间：{current_date}

用户查询："{state['research_topic']}"

请从以下维度分析用户意图：

1. **研究类型识别**：
   - 商业机会分析（投资、创业、盈利模式）
   - 技术趋势研究（AI、科技、创新）
   - 市场调研（行业分析、竞争格局）
   - 学术研究（理论分析、文献综述）
   - 个人决策（职业规划、消费选择）

2. **时间维度分析**：
   - 历史回顾（过去发展历程）
   - 现状分析（{current_year}年当前情况）
   - 趋势预测（未来3-5年发展）
   - 实时动态（最新资讯和变化）

3. **深度需求分析**：
   - 概览性了解（基础认知）
   - 深度专业分析（专家级洞察）
   - 实操指导（具体行动建议）
   - 投资决策（财务分析和风险评估）

4. **目标受众定位**：
   - 企业决策者（战略规划）
   - 投资者（投资机会）
   - 创业者（商业模式）
   - 研究人员（学术价值）
   - 普通用户（通识了解）

请以JSON格式返回分析结果：
{{
    "research_type": "具体的研究类型",
    "time_focus": "时间重点",
    "depth_level": "深度需求级别",
    "target_audience": "目标受众",
    "key_questions": ["核心问题1", "核心问题2", "核心问题3"],
    "search_strategy": "搜索策略建议",
    "analysis_framework": "推荐的分析框架"
}}"""

    response = llm.invoke([HumanMessage(content=intent_prompt)])

    # 解析意图分析结果
    try:
        import json
        intent_data = json.loads(response.content.strip())
    except:
        # 如果JSON解析失败，使用默认结构
        intent_data = {
            "research_type": "综合分析",
            "time_focus": f"{current_year}年现状分析",
            "depth_level": "深度专业分析",
            "target_audience": "决策者",
            "key_questions": ["现状如何", "趋势如何", "机会在哪"],
            "search_strategy": "多维度搜索",
            "analysis_framework": "SWOT分析"
        }

    return {
        "messages": [response],
        "user_intent": intent_data,
        "current_step": "planning"
    }

# 研究规划节点
def research_planner(state: ResearchState):
    """基于用户意图制定精准的研究计划"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月")

    # 获取用户意图信息
    intent = state.get('user_intent', {})
    research_type = intent.get('research_type', '综合分析')
    time_focus = intent.get('time_focus', f'{current_year}年现状分析')
    depth_level = intent.get('depth_level', '深度专业分析')
    target_audience = intent.get('target_audience', '决策者')
    key_questions = intent.get('key_questions', [])
    analysis_framework = intent.get('analysis_framework', 'SWOT分析')

    system_prompt = f"""你是一位资深的战略研究顾问。基于用户意图分析结果，制定精准的研究计划。

研究背景：
- 当前时间：{current_date}
- 研究主题：{state['research_topic']}
- 研究类型：{research_type}
- 时间重点：{time_focus}
- 深度需求：{depth_level}
- 目标受众：{target_audience}
- 核心问题：{', '.join(key_questions)}
- 分析框架：{analysis_framework}

请基于用户意图制定针对性的研究计划：

1. **研究目标明确化**
   - 基于用户意图细化研究目标
   - 确定关键成功指标
   - 明确交付成果要求

2. **定制化信息收集策略**
   - 针对{research_type}的专业数据源
   - 符合{time_focus}的时效性要求
   - 满足{depth_level}的信息深度

3. **个性化分析框架**
   - 采用{analysis_framework}为主要分析方法
   - 结合其他适合的分析工具
   - 确保分析结果符合{target_audience}需求

4. **精准搜索查询生成**
   - 基于核心问题生成搜索关键词
   - 考虑不同信息源的特点
   - 优化搜索策略以获得高质量信息

请生成具体的研究计划和搜索查询建议。"""

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=f"请为研究主题'{state['research_topic']}'制定精准的研究计划")
    ]

    response = llm.invoke(messages)

    # 解析研究计划
    plan_lines = response.content.split('\n')
    research_plan = [line.strip() for line in plan_lines if line.strip() and not line.strip().startswith('#')]

    return {
        "messages": [response],
        "research_plan": research_plan,
        "current_step": "query_generation"
    }

# 搜索查询生成节点
def query_generator(state: ResearchState):
    """基于用户意图和研究计划生成精准的搜索查询"""
    llm = create_llm()

    current_year = datetime.now().year

    # 获取用户意图和研究计划
    intent = state.get('user_intent', {})
    research_type = intent.get('research_type', '综合分析')
    key_questions = intent.get('key_questions', [])
    search_strategy = intent.get('search_strategy', '多维度搜索')

    query_prompt = f"""你是一位专业的信息检索专家。基于用户意图和研究计划，生成高质量的搜索查询。

研究主题：{state['research_topic']}
研究类型：{research_type}
核心问题：{', '.join(key_questions)}
搜索策略：{search_strategy}

请生成5-8个精准的搜索查询，要求：

1. **针对性强**：每个查询都针对特定的研究角度
2. **时效性好**：包含{current_year}年的时间限定
3. **专业性高**：使用行业专业术语和关键词
4. **覆盖面广**：涵盖不同维度和信息源

根据研究类型生成相应的搜索查询：

**商业机会分析类**：
- 市场规模和增长数据
- 盈利模式和商业案例
- 投资动态和融资信息
- 竞争格局和行业报告

**技术趋势研究类**：
- 技术突破和创新应用
- 专利申请和研发动态
- 行业标准和技术路线
- 应用场景和市场前景

**市场调研类**：
- 行业数据和统计报告
- 政策法规和监管动态
- 用户需求和行为分析
- 竞争对手和市场份额

请直接返回搜索查询列表，每行一个查询："""

    response = llm.invoke([HumanMessage(content=query_prompt)])

    # 解析搜索查询
    query_lines = response.content.strip().split('\n')
    search_queries = [line.strip().lstrip('- ').lstrip('• ').lstrip('1234567890. ')
                     for line in query_lines if line.strip()]

    # 过滤掉空查询和标题行
    search_queries = [q for q in search_queries if len(q) > 10 and not q.startswith('**')]

    return {
        "messages": [response],
        "search_queries": search_queries[:6],  # 限制查询数量
        "current_step": "searching"
    }

# 信息搜索节点
def information_searcher(state: ResearchState):
    """使用生成的搜索查询执行信息搜索"""
    try:
        search_tool = TavilySearchResults(
            max_results=int(os.getenv("SEARCH_MAX_RESULTS", "3")),
            search_depth="advanced",
            include_answer=True
        )

        # 获取生成的搜索查询
        search_queries = state.get('search_queries', [])

        # 如果没有生成的查询，使用默认查询
        if not search_queries:
            current_year = datetime.now().year
            topic = state['research_topic']
            search_queries = [
                f"{topic} {current_year}年最新发展",
                f"{topic} {current_year}年市场分析",
                f"{topic} {current_year}年趋势研究"
            ]

        search_results = []
        for query in search_queries:
            try:
                # 执行搜索
                results = search_tool.invoke({"query": query})
                search_results.append({
                    "query": query,
                    "response": str(results),
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                search_results.append({
                    "query": query,
                    "response": f"搜索失败: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                })

        return {
            "messages": [AIMessage(content=f"基于精准查询完成了 {len(search_results)} 个搜索")],
            "search_results": search_results,
            "current_step": "analyzing"
        }
    except Exception as e:
        return {
            "messages": [AIMessage(content=f"搜索过程出现错误: {str(e)}")],
            "search_results": [],
            "current_step": "analyzing"
        }

# 分析综合节点
def research_analyzer(state: ResearchState):
    """基于用户意图进行针对性分析"""
    llm = create_llm()

    current_date = datetime.now().strftime("%Y年%m月%d日")

    # 获取用户意图信息
    intent = state.get('user_intent', {})
    research_type = intent.get('research_type', '综合分析')
    depth_level = intent.get('depth_level', '深度专业分析')
    target_audience = intent.get('target_audience', '决策者')
    key_questions = intent.get('key_questions', [])
    analysis_framework = intent.get('analysis_framework', 'SWOT分析')

    # 准备分析内容
    search_content = "\n\n".join([
        f"搜索查询: {result['query']}\n结果: {result['response']}"
        for result in state['search_results']
    ])

    # 根据研究类型定制分析提示词
    if research_type == "商业机会分析":
        analysis_focus = """
## 商业机会深度分析
1. **市场机会量化评估**
   - 市场规模和增长潜力
   - 目标客户群体和需求分析
   - 竞争强度和进入壁垒

2. **盈利模式分析**
   - 收入来源和盈利结构
   - 成本结构和利润空间
   - 投资回报和风险评估

3. **商业可行性评估**
   - 技术可行性和实现难度
   - 资源需求和能力要求
   - 时间窗口和执行路径"""

    elif research_type == "技术趋势研究":
        analysis_focus = """
## 技术趋势深度分析
1. **技术发展态势**
   - 核心技术突破和创新点
   - 技术成熟度和应用现状
   - 技术路线图和发展预测

2. **应用场景分析**
   - 主要应用领域和用例
   - 技术价值和效益评估
   - 推广障碍和解决方案

3. **产业影响评估**
   - 对传统行业的颠覆性影响
   - 新兴产业机会和生态构建
   - 政策支持和监管考量"""

    else:
        analysis_focus = """
## 综合深度分析
1. **现状全面评估**
   - 发展水平和竞争地位
   - 关键指标和性能表现
   - 优势劣势对比分析

2. **趋势预测分析**
   - 发展趋势和变化驱动因素
   - 机会窗口和威胁识别
   - 情景分析和应对策略

3. **战略建议制定**
   - 短中长期发展策略
   - 资源配置和能力建设
   - 风险管控和应急预案"""

    analysis_prompt = f"""你是一位{research_type}领域的资深专家，拥有15年以上的专业经验。请基于用户意图进行针对性的深度分析。

研究主题: {state['research_topic']}
分析时间: {current_date}
研究类型: {research_type}
深度需求: {depth_level}
目标受众: {target_audience}
核心问题: {', '.join(key_questions)}
分析框架: {analysis_framework}

搜索结果:
{search_content}

请按以下定制化框架进行分析：

{analysis_focus}

## 核心洞察提取
- 针对核心问题的直接回答
- 基于数据的关键发现
- 超越表面现象的深层洞察

## {analysis_framework}
- 采用{analysis_framework}进行系统分析
- 结合其他适合的分析工具
- 确保分析结果符合{target_audience}的需求

## 量化分析和预测
- 提供具体的数字和比例
- 基于趋势的量化预测
- 关键指标的监控建议

## 实施建议
- 针对{target_audience}的具体建议
- 分阶段的实施路径
- 成功关键因素和风险控制

请确保分析深度符合{depth_level}的要求，提供真正有价值的专业洞察。"""

    response = llm.invoke([HumanMessage(content=analysis_prompt)])

    return {
        "messages": [response],
        "analysis_results": [response.content],
        "current_step": "reporting"
    }

# 报告生成节点
def report_generator(state: ResearchState):
    """生成最终研究报告"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月%d日")

    # 整合所有信息
    all_content = {
        "topic": state['research_topic'],
        "plan": state['research_plan'],
        "search_results": state['search_results'],
        "analysis": state['analysis_results']
    }

    report_prompt = f"""你是一位顶级的商业咨询顾问和报告撰写专家，曾为多家世界500强企业提供战略咨询服务。请基于研究内容撰写一份高质量的深度研究报告。

研究主题: {all_content['topic']}
报告生成时间: {current_date}

报告要求：
- 具备咨询级别的专业深度和商业价值
- 提供可执行的战略建议和实施路径
- 包含量化分析和数据支撑
- 结构清晰、逻辑严密、洞察深刻

研究计划:
{chr(10).join(all_content['plan'])}

深度分析结果:
{chr(10).join(all_content['analysis'])}

请按以下专业结构生成报告：

# {all_content['topic']} - {current_year}年战略研究报告

## 执行摘要 (Executive Summary)
- **核心发现**: 3-5个最关键的洞察，每个都有数据支撑
- **战略建议**: 3个最重要的行动建议
- **投资回报预期**: 量化的收益预测（如适用）
- **风险评级**: 整体风险水平评估

## 研究背景与方法论
- **研究范围**: 明确的研究边界和时间范围
- **数据来源**: 信息来源的可靠性分析
- **分析框架**: 使用的分析方法和工具
- **研究限制**: 承认的局限性和假设条件

## 市场环境与趋势分析
- **宏观环境**: PEST分析（政治、经济、社会、技术）
- **行业态势**: 市场规模、增长率、竞争格局
- **关键驱动因素**: 影响发展的核心变量
- **趋势预测**: 基于数据的未来3-5年趋势

## 深度洞察与发现
- **机会识别**: 具体的市场机会和价值评估
- **成功案例**: 标杆企业的成功模式分析
- **失败教训**: 典型失败案例的原因剖析
- **创新模式**: 新兴的商业模式和技术应用

## 战略建议与实施路径
- **短期行动计划** (0-6个月): 具体的执行步骤
- **中期发展策略** (6-18个月): 能力建设和市场拓展
- **长期愿景规划** (1-3年): 战略定位和生态构建
- **资源配置建议**: 人力、资金、技术的投入建议

## 风险评估与缓解策略
- **风险矩阵**: 概率-影响度分析
- **关键风险点**: 最需要关注的3-5个风险
- **缓解措施**: 针对性的风险管控方案
- **应急预案**: 极端情况下的应对策略

## 量化分析与预测
- **市场规模预测**: 未来3年的市场容量
- **投资回报分析**: ROI、IRR等财务指标
- **关键指标监控**: KPI设定和追踪方法
- **里程碑设定**: 阶段性目标和时间节点

## 结论与下一步行动
- **核心结论**: 最重要的3个结论
- **优先级排序**: 建议的执行优先级
- **成功关键因素**: 决定成败的核心要素
- **后续研究建议**: 需要进一步深入的领域

## 附录
- **数据来源**: 详细的信息来源列表
- **专业术语**: 关键概念的定义
- **更新说明**: 数据截止时间为{current_date}

请确保报告具有以下特点：
1. **数据驱动**: 每个观点都有具体数据支撑
2. **洞察深刻**: 提供超越表面现象的深层分析
3. **实操性强**: 建议具体可执行，有明确的时间表
4. **逻辑严密**: 结构清晰，论证充分
5. **前瞻性**: 不仅分析现状，更要预测未来"""

    response = llm.invoke([HumanMessage(content=report_prompt)])

    return {
        "messages": [response],
        "final_report": response.content,
        "current_step": "completed"
    }

# 创建研究图
def create_research_graph():
    """创建基于意图分析的LangGraph研究工作流"""
    workflow = StateGraph(ResearchState)

    # 添加节点
    workflow.add_node("intent_analyzer", intent_analyzer)
    workflow.add_node("planner", research_planner)
    workflow.add_node("query_generator", query_generator)
    workflow.add_node("searcher", information_searcher)
    workflow.add_node("analyzer", research_analyzer)
    workflow.add_node("reporter", report_generator)

    # 定义基于意图的流程
    workflow.add_edge(START, "intent_analyzer")
    workflow.add_edge("intent_analyzer", "planner")
    workflow.add_edge("planner", "query_generator")
    workflow.add_edge("query_generator", "searcher")
    workflow.add_edge("searcher", "analyzer")
    workflow.add_edge("analyzer", "reporter")
    workflow.add_edge("reporter", END)

    # 添加检查点
    memory = MemorySaver()
    graph = workflow.compile(checkpointer=memory)

    return graph

# 主应用界面
def main():
    st.title("🔍 Deep Research Assistant")
    st.markdown("**智能意图识别** + **精准搜索** + **深度分析** = **专业研究报告**")

    # 添加功能亮点
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("🎯", "意图识别", "自动分析用户需求")
    with col2:
        st.metric("🔍", "精准搜索", "定制化查询生成")
    with col3:
        st.metric("📊", "深度分析", "专业框架分析")
    with col4:
        st.metric("📄", "智能报告", "针对性内容生成")
    
    # 初始化
    initialize_session_state()
    
    # 检查API密钥配置
    missing_keys = check_api_keys()

    # 侧边栏配置
    with st.sidebar:
        st.header("🛠️ 配置状态")

        # 显示API密钥状态
        if missing_keys:
            st.error("❌ 缺少必要的API密钥")
            for key in missing_keys:
                st.write(f"- {key}")
            st.info("请在 .env 文件中配置API密钥")
        else:
            st.success("✅ API密钥配置完成")

        # 显示模型配置
        st.subheader("🤖 模型配置")
        model_provider = os.getenv("MODEL_PROVIDER", "openai")
        model_name = os.getenv("MODEL_NAME", "gpt-4o-mini")
        base_url = os.getenv("MODEL_BASE_URL", "官方API")
        temperature = os.getenv("MODEL_TEMPERATURE", "0.1")
        max_tokens = os.getenv("MODEL_MAX_TOKENS", "4000")
        search_results = os.getenv("SEARCH_MAX_RESULTS", "5")

        st.write(f"**提供商**: {model_provider}")
        st.write(f"**模型**: {model_name}")
        st.write(f"**API地址**: {base_url}")
        st.write(f"**温度**: {temperature}")
        st.write(f"**最大输出**: {max_tokens} tokens")
        st.write(f"**搜索结果数**: {search_results}")

        st.divider()

        # 研究状态显示
        st.header("📊 研究状态")
        current_step = st.session_state.research_state.get("current_step", "intent_analysis")

        steps = ["intent_analysis", "planning", "query_generation", "searching", "analyzing", "reporting", "completed"]
        step_names = ["意图分析", "研究规划", "查询生成", "信息搜索", "深度分析", "报告生成", "完成"]

        for i, (step, name) in enumerate(zip(steps, step_names)):
            if step == current_step:
                st.success(f"✅ {name}")
            elif steps.index(current_step) > i:
                st.success(f"✅ {name}")
            else:
                st.info(f"⏳ {name}")

        # 显示用户意图分析结果
        if st.session_state.research_state.get("user_intent"):
            st.divider()
            st.subheader("🎯 意图分析")
            intent = st.session_state.research_state["user_intent"]
            st.write(f"**研究类型**: {intent.get('research_type', '未知')}")
            st.write(f"**目标受众**: {intent.get('target_audience', '未知')}")
            st.write(f"**深度需求**: {intent.get('depth_level', '未知')}")

            if intent.get('key_questions'):
                st.write("**核心问题**:")
                for q in intent['key_questions'][:3]:
                    st.write(f"- {q}")

        # 显示生成的搜索查询
        if st.session_state.research_state.get("search_queries"):
            st.divider()
            st.subheader("🔍 搜索查询")
            queries = st.session_state.research_state["search_queries"]
            for i, query in enumerate(queries[:3], 1):
                st.write(f"{i}. {query}")
            if len(queries) > 3:
                st.write(f"... 共{len(queries)}个查询")
    
    # 主界面
    col1, col2 = st.columns([2, 1])

    with col1:
        st.header("🎯 研究主题")
        research_topic = st.text_area(
            "请输入您想要深入研究的主题：",
            height=100,
            placeholder="例如：人工智能在医疗诊断中的应用现状和发展趋势"
        )

        # 检查是否可以开始研究
        can_start_research = len(missing_keys) == 0

        if st.button("🚀 开始深度研究", type="primary", disabled=not can_start_research):
            if not can_start_research:
                st.error("请先在 .env 文件中配置必要的API密钥")
            elif research_topic:
                with st.spinner("正在初始化研究工作流..."):
                    # 创建研究图
                    st.session_state.research_graph = create_research_graph()

                    # 更新研究状态
                    st.session_state.research_state["research_topic"] = research_topic
                    st.session_state.research_state["current_step"] = "planning"

                    st.success("研究工作流已启动！")
                    st.rerun()
            else:
                st.warning("请输入研究主题")

    with col2:
        st.header("📋 快速开始")
        st.markdown("""
        **使用步骤：**
        1. 在侧边栏输入API密钥
        2. 输入研究主题
        3. 点击开始研究
        4. 等待AI完成分析
        5. 查看详细报告

        **示例主题：**
        - 区块链技术在供应链管理中的应用
        - 可再生能源的最新发展趋势
        - 远程工作对企业文化的影响
        """)

    # 执行研究工作流
    if st.session_state.research_graph and st.session_state.research_state["research_topic"]:
        execute_research_workflow()

    # 显示研究结果
    display_research_results()

def execute_research_workflow():
    """执行研究工作流"""
    if st.session_state.research_state["current_step"] != "completed":
        with st.spinner("🔍 正在执行深度研究..."):
            try:
                # 准备初始状态
                initial_state = {
                    "messages": [],
                    "research_topic": st.session_state.research_state["research_topic"],
                    "user_intent": {},
                    "research_plan": [],
                    "search_queries": [],
                    "search_results": [],
                    "analysis_results": [],
                    "final_report": "",
                    "current_step": "intent_analysis"
                }

                # 执行工作流
                config = {"configurable": {"thread_id": "research_thread"}}
                final_state = st.session_state.research_graph.invoke(initial_state, config)

                # 更新会话状态
                st.session_state.research_state.update(final_state)

                st.success("✅ 研究完成！")
                st.rerun()

            except Exception as e:
                st.error(f"研究过程中出现错误：{str(e)}")

def display_research_results():
    """显示研究结果"""
    if st.session_state.research_state["research_topic"]:
        st.header("📊 研究结果")

        # 创建标签页
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["🎯 意图分析", "📋 研究计划", "🔍 搜索结果", "📈 分析报告", "📄 最终报告"])

        with tab1:
            st.subheader("用户意图分析")
            if st.session_state.research_state.get("user_intent"):
                intent = st.session_state.research_state["user_intent"]

                col1, col2 = st.columns(2)
                with col1:
                    st.write("**研究类型**")
                    st.info(intent.get('research_type', '未识别'))

                    st.write("**时间重点**")
                    st.info(intent.get('time_focus', '未识别'))

                    st.write("**深度需求**")
                    st.info(intent.get('depth_level', '未识别'))

                with col2:
                    st.write("**目标受众**")
                    st.info(intent.get('target_audience', '未识别'))

                    st.write("**分析框架**")
                    st.info(intent.get('analysis_framework', '未识别'))

                    st.write("**搜索策略**")
                    st.info(intent.get('search_strategy', '未识别'))

                if intent.get('key_questions'):
                    st.write("**核心问题**")
                    for i, question in enumerate(intent['key_questions'], 1):
                        st.write(f"{i}. {question}")

                # 显示生成的搜索查询
                if st.session_state.research_state.get("search_queries"):
                    st.write("**生成的搜索查询**")
                    for i, query in enumerate(st.session_state.research_state["search_queries"], 1):
                        st.write(f"{i}. {query}")
            else:
                st.info("意图分析进行中...")

        with tab2:
            st.subheader("研究计划")
            if st.session_state.research_state["research_plan"]:
                for plan_item in st.session_state.research_state["research_plan"]:
                    if plan_item.strip():
                        st.write(plan_item)
            else:
                st.info("研究计划生成中...")

        with tab3:
            st.subheader("搜索结果")
            if st.session_state.research_state["search_results"]:
                for i, result in enumerate(st.session_state.research_state["search_results"], 1):
                    with st.expander(f"搜索 {i}: {result['query'][:50]}..."):
                        st.write("**搜索查询:**")
                        st.code(result['query'])
                        st.write("**搜索结果:**")
                        st.write(result['response'])
                        st.caption(f"搜索时间: {result['timestamp']}")
            else:
                st.info("搜索结果生成中...")

        with tab4:
            st.subheader("深度分析报告")
            if st.session_state.research_state["analysis_results"]:
                for analysis in st.session_state.research_state["analysis_results"]:
                    st.markdown(analysis)
            else:
                st.info("分析报告生成中...")

        with tab5:
            st.subheader("最终研究报告")
            if st.session_state.research_state["final_report"]:
                st.markdown(st.session_state.research_state["final_report"])

                # 添加下载按钮
                st.download_button(
                    label="📥 下载完整报告",
                    data=st.session_state.research_state["final_report"],
                    file_name=f"research_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                    mime="text/markdown"
                )
            else:
                st.info("最终报告生成中...")

if __name__ == "__main__":
    main()

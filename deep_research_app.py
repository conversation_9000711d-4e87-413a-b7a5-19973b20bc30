import streamlit as st
import os
from typing import TypedDict, Annotated, List, Dict, Any
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_community.tools.tavily_search import TavilySearchResults
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langgraph.prebuilt import ToolNode, tools_condition
from langgraph.checkpoint.memory import MemorySaver
import json
from datetime import datetime
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 配置检查和初始化
def check_api_keys():
    """检查必要的API密钥是否已配置"""
    openai_key = os.getenv("OPENAI_API_KEY")
    tavily_key = os.getenv("TAVILY_API_KEY")

    missing_keys = []
    if not openai_key:
        missing_keys.append("OPENAI_API_KEY")
    if not tavily_key:
        missing_keys.append("TAVILY_API_KEY")

    return missing_keys

# 创建LLM实例（支持第三方模型）
def create_llm():
    """创建LLM实例，支持多种模型提供商和参数配置"""
    # 从环境变量获取模型配置
    model_provider = os.getenv("MODEL_PROVIDER", "openai")
    model_name = os.getenv("MODEL_NAME", "gpt-4o-mini")
    base_url = os.getenv("MODEL_BASE_URL")
    api_key = os.getenv("OPENAI_API_KEY")

    # 获取模型参数配置
    temperature = float(os.getenv("MODEL_TEMPERATURE", "0.1"))
    max_tokens = int(os.getenv("MODEL_MAX_TOKENS", "4000"))

    if model_provider.lower() == "openai":
        if base_url:
            # 使用第三方OpenAI兼容API
            return ChatOpenAI(
                model=model_name,
                api_key=api_key,
                base_url=base_url,
                temperature=temperature,
                max_tokens=max_tokens
            )
        else:
            # 使用官方OpenAI API
            return ChatOpenAI(
                model=model_name,
                api_key=api_key,
                temperature=temperature,
                max_tokens=max_tokens
            )
    else:
        # 可以在这里添加其他模型提供商的支持
        # 例如：Anthropic, Google, etc.
        return ChatOpenAI(
            model=model_name,
            api_key=api_key,
            base_url=base_url,
            temperature=temperature,
            max_tokens=max_tokens
        )

# 页面配置
st.set_page_config(
    page_title="Deep Research Assistant",
    page_icon="🔍",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 定义状态结构
class ResearchState(TypedDict):
    messages: Annotated[List, add_messages]
    research_topic: str
    research_plan: List[str]
    search_results: List[Dict[str, Any]]
    analysis_results: List[str]
    final_report: str
    current_step: str

# 初始化会话状态
def initialize_session_state():
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "research_state" not in st.session_state:
        st.session_state.research_state = {
            "messages": [],
            "research_topic": "",
            "research_plan": [],
            "search_results": [],
            "analysis_results": [],
            "final_report": "",
            "current_step": "planning"
        }
    if "research_graph" not in st.session_state:
        st.session_state.research_graph = None

# 创建研究工具
def create_research_tools():
    """创建研究所需的工具"""
    # 从环境变量获取搜索配置
    max_results = int(os.getenv("SEARCH_MAX_RESULTS", "5"))

    search_tool = TavilySearchResults(
        max_results=max_results,
        search_depth="advanced",
        include_answer=True,
        include_raw_content=True
    )
    return [search_tool]

# 研究规划节点
def research_planner(state: ResearchState):
    """制定研究计划"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月")

    system_prompt = f"""你是一位资深的战略研究顾问，拥有多年的行业分析经验。你需要为客户制定一个深度、全面的研究计划。

研究背景：
- 当前时间：{current_date}
- 研究时效性：重点关注{current_year}年的最新发展和趋势
- 研究深度：需要提供具有商业价值和实操性的深度洞察

请制定一个多维度、多层次的研究框架，包括：

1. **核心研究问题定义**
   - 主要研究问题和子问题
   - 关键假设和验证点
   - 研究边界和限制条件

2. **多维度信息收集策略**
   - 市场数据和行业报告
   - 政策法规和监管动态
   - 技术发展和创新趋势
   - 竞争格局和商业模式
   - 用户需求和行为变化

3. **深度分析框架**
   - SWOT分析（优势、劣势、机会、威胁）
   - PEST分析（政治、经济、社会、技术）
   - 价值链分析
   - 风险评估矩阵
   - 趋势预测模型

4. **洞察提取方法**
   - 数据交叉验证
   - 专家观点整合
   - 案例对比分析
   - 量化建模分析

研究计划应该具备：战略高度、数据支撑、逻辑严密、实操性强的特点。"""

    messages = [
        SystemMessage(content=system_prompt),
        HumanMessage(content=f"请为以下研究主题制定深度研究计划：{state['research_topic']}")
    ]

    response = llm.invoke(messages)

    # 解析研究计划
    plan_lines = response.content.split('\n')
    research_plan = [line.strip() for line in plan_lines if line.strip() and not line.strip().startswith('#')]

    return {
        "messages": [response],
        "research_plan": research_plan,
        "current_step": "searching"
    }

# 信息搜索节点
def information_searcher(state: ResearchState):
    """执行信息搜索"""
    try:
        search_tool = TavilySearchResults(
            max_results=3,
            search_depth="advanced",
            include_answer=True
        )

        # 获取当前年份
        current_year = datetime.now().year

        # 基于研究计划生成搜索查询
        topic = state['research_topic']

        # 生成多维度、深度的搜索查询
        base_queries = [
            f"{topic} {current_year}年最新发展趋势",
            f"{topic} {current_year}年市场分析报告",
            f"{topic} {current_year}年行业数据统计"
        ]

        # 根据主题类型添加专业化搜索查询
        if any(keyword in topic.lower() for keyword in ["赚钱", "盈利", "投资", "商业", "创业"]):
            base_queries.extend([
                f"{topic} {current_year}年盈利模式分析",
                f"{topic} {current_year}年投资回报数据",
                f"{topic} {current_year}年成功案例研究"
            ])
        elif any(keyword in topic.lower() for keyword in ["技术", "科技", "ai", "人工智能"]):
            base_queries.extend([
                f"{topic} {current_year}年技术突破",
                f"{topic} {current_year}年应用场景",
                f"{topic} {current_year}年市场规模"
            ])
        elif any(keyword in topic.lower() for keyword in ["行业", "市场", "发展"]):
            base_queries.extend([
                f"{topic} {current_year}年竞争格局",
                f"{topic} {current_year}年政策影响",
                f"{topic} {current_year}年风险分析"
            ])

        # 如果主题包含"今年"、"最新"等词汇，添加更具体的时间查询
        if any(keyword in topic for keyword in ["今年", "最新", "当前", "现在"]):
            base_queries.extend([
                f"{topic.replace('今年', str(current_year))} {current_year}年深度分析",
                f"{topic} {current_year}年专家观点",
                f"{topic} {current_year}年前景预测"
            ])

        search_results = []
        for query in base_queries[:5]:  # 增加搜索次数以获得更丰富的信息
            try:
                # 直接使用搜索工具
                results = search_tool.invoke({"query": query})
                search_results.append({
                    "query": query,
                    "response": str(results),
                    "timestamp": datetime.now().isoformat()
                })
            except Exception as e:
                search_results.append({
                    "query": query,
                    "response": f"搜索失败: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                })

        return {
            "messages": [AIMessage(content=f"完成了 {len(search_results)} 个搜索查询")],
            "search_results": search_results,
            "current_step": "analyzing"
        }
    except Exception as e:
        return {
            "messages": [AIMessage(content=f"搜索过程出现错误: {str(e)}")],
            "search_results": [],
            "current_step": "analyzing"
        }

# 分析综合节点
def research_analyzer(state: ResearchState):
    """分析和综合研究结果"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月%d日")

    # 准备分析内容
    search_content = "\n\n".join([
        f"搜索查询: {result['query']}\n结果: {result['response']}"
        for result in state['search_results']
    ])

    analysis_prompt = f"""你是一位资深的战略分析师和行业专家，拥有15年以上的深度研究经验。请对以下信息进行专业、深入的分析。

研究主题: {state['research_topic']}
分析时间: {current_date}

分析要求：
- 提供具有商业价值和实操性的深度洞察
- 基于数据进行量化分析，避免空泛的描述
- 识别关键成功因素和潜在风险点
- 提供具体的行动建议和实施路径

搜索结果:
{search_content}

请按以下框架进行深度分析：

## 1. 核心洞察提取
- 从数据中提取3-5个最关键的发现
- 每个发现都要有具体的数据支撑
- 分析这些发现背后的深层原因

## 2. 多维度SWOT分析
**优势 (Strengths):**
- 具体列出当前的竞争优势
- 量化优势的程度和持续性

**劣势 (Weaknesses):**
- 识别关键短板和限制因素
- 分析劣势的影响程度

**机会 (Opportunities):**
- 基于{current_year}年趋势识别具体机会
- 评估机会的市场规模和时间窗口

**威胁 (Threats):**
- 识别潜在风险和挑战
- 评估威胁的概率和影响程度

## 3. 关键成功因素分析
- 识别决定成功的3-5个关键因素
- 分析每个因素的重要性权重
- 提供针对性的能力建设建议

## 4. 风险评估矩阵
- 列出主要风险点
- 评估风险概率和影响程度
- 提供风险缓解策略

## 5. 量化预测和建议
- 基于数据趋势进行量化预测
- 提供具体的时间节点和里程碑
- 给出可执行的行动计划

请确保每个分析点都有具体的数据支撑，避免空泛的表述，提供真正有价值的商业洞察。"""

    response = llm.invoke([HumanMessage(content=analysis_prompt)])

    return {
        "messages": [response],
        "analysis_results": [response.content],
        "current_step": "reporting"
    }

# 报告生成节点
def report_generator(state: ResearchState):
    """生成最终研究报告"""
    llm = create_llm()

    current_year = datetime.now().year
    current_date = datetime.now().strftime("%Y年%m月%d日")

    # 整合所有信息
    all_content = {
        "topic": state['research_topic'],
        "plan": state['research_plan'],
        "search_results": state['search_results'],
        "analysis": state['analysis_results']
    }

    report_prompt = f"""你是一位顶级的商业咨询顾问和报告撰写专家，曾为多家世界500强企业提供战略咨询服务。请基于研究内容撰写一份高质量的深度研究报告。

研究主题: {all_content['topic']}
报告生成时间: {current_date}

报告要求：
- 具备咨询级别的专业深度和商业价值
- 提供可执行的战略建议和实施路径
- 包含量化分析和数据支撑
- 结构清晰、逻辑严密、洞察深刻

研究计划:
{chr(10).join(all_content['plan'])}

深度分析结果:
{chr(10).join(all_content['analysis'])}

请按以下专业结构生成报告：

# {all_content['topic']} - {current_year}年战略研究报告

## 执行摘要 (Executive Summary)
- **核心发现**: 3-5个最关键的洞察，每个都有数据支撑
- **战略建议**: 3个最重要的行动建议
- **投资回报预期**: 量化的收益预测（如适用）
- **风险评级**: 整体风险水平评估

## 研究背景与方法论
- **研究范围**: 明确的研究边界和时间范围
- **数据来源**: 信息来源的可靠性分析
- **分析框架**: 使用的分析方法和工具
- **研究限制**: 承认的局限性和假设条件

## 市场环境与趋势分析
- **宏观环境**: PEST分析（政治、经济、社会、技术）
- **行业态势**: 市场规模、增长率、竞争格局
- **关键驱动因素**: 影响发展的核心变量
- **趋势预测**: 基于数据的未来3-5年趋势

## 深度洞察与发现
- **机会识别**: 具体的市场机会和价值评估
- **成功案例**: 标杆企业的成功模式分析
- **失败教训**: 典型失败案例的原因剖析
- **创新模式**: 新兴的商业模式和技术应用

## 战略建议与实施路径
- **短期行动计划** (0-6个月): 具体的执行步骤
- **中期发展策略** (6-18个月): 能力建设和市场拓展
- **长期愿景规划** (1-3年): 战略定位和生态构建
- **资源配置建议**: 人力、资金、技术的投入建议

## 风险评估与缓解策略
- **风险矩阵**: 概率-影响度分析
- **关键风险点**: 最需要关注的3-5个风险
- **缓解措施**: 针对性的风险管控方案
- **应急预案**: 极端情况下的应对策略

## 量化分析与预测
- **市场规模预测**: 未来3年的市场容量
- **投资回报分析**: ROI、IRR等财务指标
- **关键指标监控**: KPI设定和追踪方法
- **里程碑设定**: 阶段性目标和时间节点

## 结论与下一步行动
- **核心结论**: 最重要的3个结论
- **优先级排序**: 建议的执行优先级
- **成功关键因素**: 决定成败的核心要素
- **后续研究建议**: 需要进一步深入的领域

## 附录
- **数据来源**: 详细的信息来源列表
- **专业术语**: 关键概念的定义
- **更新说明**: 数据截止时间为{current_date}

请确保报告具有以下特点：
1. **数据驱动**: 每个观点都有具体数据支撑
2. **洞察深刻**: 提供超越表面现象的深层分析
3. **实操性强**: 建议具体可执行，有明确的时间表
4. **逻辑严密**: 结构清晰，论证充分
5. **前瞻性**: 不仅分析现状，更要预测未来"""

    response = llm.invoke([HumanMessage(content=report_prompt)])

    return {
        "messages": [response],
        "final_report": response.content,
        "current_step": "completed"
    }

# 创建研究图
def create_research_graph():
    """创建LangGraph研究工作流"""
    workflow = StateGraph(ResearchState)

    # 添加节点
    workflow.add_node("planner", research_planner)
    workflow.add_node("searcher", information_searcher)
    workflow.add_node("analyzer", research_analyzer)
    workflow.add_node("reporter", report_generator)

    # 定义简化的流程
    workflow.add_edge(START, "planner")
    workflow.add_edge("planner", "searcher")
    workflow.add_edge("searcher", "analyzer")
    workflow.add_edge("analyzer", "reporter")
    workflow.add_edge("reporter", END)

    # 添加检查点
    memory = MemorySaver()
    graph = workflow.compile(checkpointer=memory)

    return graph

# 主应用界面
def main():
    st.title("🔍 Deep Research Assistant")
    st.markdown("基于 LangGraph 和 Streamlit 的智能深度研究助手")
    
    # 初始化
    initialize_session_state()
    
    # 检查API密钥配置
    missing_keys = check_api_keys()

    # 侧边栏配置
    with st.sidebar:
        st.header("🛠️ 配置状态")

        # 显示API密钥状态
        if missing_keys:
            st.error("❌ 缺少必要的API密钥")
            for key in missing_keys:
                st.write(f"- {key}")
            st.info("请在 .env 文件中配置API密钥")
        else:
            st.success("✅ API密钥配置完成")

        # 显示模型配置
        st.subheader("🤖 模型配置")
        model_provider = os.getenv("MODEL_PROVIDER", "openai")
        model_name = os.getenv("MODEL_NAME", "gpt-4o-mini")
        base_url = os.getenv("MODEL_BASE_URL", "官方API")
        temperature = os.getenv("MODEL_TEMPERATURE", "0.1")
        max_tokens = os.getenv("MODEL_MAX_TOKENS", "4000")
        search_results = os.getenv("SEARCH_MAX_RESULTS", "5")

        st.write(f"**提供商**: {model_provider}")
        st.write(f"**模型**: {model_name}")
        st.write(f"**API地址**: {base_url}")
        st.write(f"**温度**: {temperature}")
        st.write(f"**最大输出**: {max_tokens} tokens")
        st.write(f"**搜索结果数**: {search_results}")

        st.divider()

        # 研究状态显示
        st.header("📊 研究状态")
        current_step = st.session_state.research_state.get("current_step", "planning")

        steps = ["planning", "searching", "analyzing", "reporting", "completed"]
        step_names = ["规划", "搜索", "分析", "报告", "完成"]

        for i, (step, name) in enumerate(zip(steps, step_names)):
            if step == current_step:
                st.success(f"✅ {name}")
            elif steps.index(current_step) > i:
                st.success(f"✅ {name}")
            else:
                st.info(f"⏳ {name}")
    
    # 主界面
    col1, col2 = st.columns([2, 1])

    with col1:
        st.header("🎯 研究主题")
        research_topic = st.text_area(
            "请输入您想要深入研究的主题：",
            height=100,
            placeholder="例如：人工智能在医疗诊断中的应用现状和发展趋势"
        )

        # 检查是否可以开始研究
        can_start_research = len(missing_keys) == 0

        if st.button("🚀 开始深度研究", type="primary", disabled=not can_start_research):
            if not can_start_research:
                st.error("请先在 .env 文件中配置必要的API密钥")
            elif research_topic:
                with st.spinner("正在初始化研究工作流..."):
                    # 创建研究图
                    st.session_state.research_graph = create_research_graph()

                    # 更新研究状态
                    st.session_state.research_state["research_topic"] = research_topic
                    st.session_state.research_state["current_step"] = "planning"

                    st.success("研究工作流已启动！")
                    st.rerun()
            else:
                st.warning("请输入研究主题")

    with col2:
        st.header("📋 快速开始")
        st.markdown("""
        **使用步骤：**
        1. 在侧边栏输入API密钥
        2. 输入研究主题
        3. 点击开始研究
        4. 等待AI完成分析
        5. 查看详细报告

        **示例主题：**
        - 区块链技术在供应链管理中的应用
        - 可再生能源的最新发展趋势
        - 远程工作对企业文化的影响
        """)

    # 执行研究工作流
    if st.session_state.research_graph and st.session_state.research_state["research_topic"]:
        execute_research_workflow()

    # 显示研究结果
    display_research_results()

def execute_research_workflow():
    """执行研究工作流"""
    if st.session_state.research_state["current_step"] != "completed":
        with st.spinner("🔍 正在执行深度研究..."):
            try:
                # 准备初始状态
                initial_state = {
                    "messages": [],
                    "research_topic": st.session_state.research_state["research_topic"],
                    "research_plan": [],
                    "search_results": [],
                    "analysis_results": [],
                    "final_report": "",
                    "current_step": "planning"
                }

                # 执行工作流
                config = {"configurable": {"thread_id": "research_thread"}}
                final_state = st.session_state.research_graph.invoke(initial_state, config)

                # 更新会话状态
                st.session_state.research_state.update(final_state)

                st.success("✅ 研究完成！")
                st.rerun()

            except Exception as e:
                st.error(f"研究过程中出现错误：{str(e)}")

def display_research_results():
    """显示研究结果"""
    if st.session_state.research_state["research_topic"]:
        st.header("📊 研究结果")

        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["📋 研究计划", "🔍 搜索结果", "📈 分析报告", "📄 最终报告"])

        with tab1:
            st.subheader("研究计划")
            if st.session_state.research_state["research_plan"]:
                for i, plan_item in enumerate(st.session_state.research_state["research_plan"], 1):
                    st.write(f"{i}. {plan_item}")
            else:
                st.info("研究计划生成中...")

        with tab2:
            st.subheader("搜索结果")
            if st.session_state.research_state["search_results"]:
                for i, result in enumerate(st.session_state.research_state["search_results"], 1):
                    with st.expander(f"搜索 {i}: {result['query']}"):
                        st.write("**搜索结果:**")
                        st.write(result['response'])
                        st.caption(f"搜索时间: {result['timestamp']}")
            else:
                st.info("搜索结果生成中...")

        with tab3:
            st.subheader("分析报告")
            if st.session_state.research_state["analysis_results"]:
                for analysis in st.session_state.research_state["analysis_results"]:
                    st.markdown(analysis)
            else:
                st.info("分析报告生成中...")

        with tab4:
            st.subheader("最终报告")
            if st.session_state.research_state["final_report"]:
                st.markdown(st.session_state.research_state["final_report"])

                # 添加下载按钮
                st.download_button(
                    label="📥 下载报告",
                    data=st.session_state.research_state["final_report"],
                    file_name=f"research_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md",
                    mime="text/markdown"
                )
            else:
                st.info("最终报告生成中...")

if __name__ == "__main__":
    main()

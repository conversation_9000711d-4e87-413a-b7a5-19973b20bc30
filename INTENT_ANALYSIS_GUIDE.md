# 意图分析功能指南 - Deep Research Assistant

## 🎯 功能概述

新版本的Deep Research Assistant引入了智能意图分析功能，能够：
- 自动识别用户的真实研究需求
- 根据意图生成精准的搜索查询
- 提供针对性的分析框架
- 生成符合用户期望的专业报告

## 🔍 意图识别维度

### 1. 研究类型识别
系统会自动识别以下研究类型：

**商业机会分析**
- 关键词：赚钱、盈利、投资、创业、商业模式
- 分析重点：市场机会、盈利模式、投资回报
- 输出特点：量化分析、ROI评估、实施路径

**技术趋势研究**
- 关键词：AI、科技、技术、创新、突破
- 分析重点：技术发展、应用场景、产业影响
- 输出特点：技术路线图、应用前景、标准分析

**市场调研**
- 关键词：行业、市场、发展、竞争、格局
- 分析重点：市场规模、竞争态势、政策影响
- 输出特点：PEST分析、竞争格局、趋势预测

**学术研究**
- 关键词：理论、研究、分析、综述、学术
- 分析重点：理论框架、文献综述、研究方法
- 输出特点：学术严谨、理论深度、方法论

**个人决策**
- 关键词：选择、决策、建议、个人、职业
- 分析重点：选项对比、决策因素、风险评估
- 输出特点：实用建议、风险提示、行动指南

### 2. 时间维度分析
- **历史回顾**：过去发展历程和经验教训
- **现状分析**：当前情况和最新动态
- **趋势预测**：未来发展方向和机会
- **实时动态**：最新资讯和变化

### 3. 深度需求分析
- **概览性了解**：基础认知和入门介绍
- **深度专业分析**：专家级洞察和深度剖析
- **实操指导**：具体行动建议和实施方案
- **投资决策**：财务分析和风险评估

### 4. 目标受众定位
- **企业决策者**：战略规划和商业决策
- **投资者**：投资机会和风险评估
- **创业者**：商业模式和市场机会
- **研究人员**：学术价值和理论深度
- **普通用户**：通识了解和实用建议

## 🚀 使用技巧

### 1. 优化查询表述

**推荐的查询格式：**
```
[时间] + [主题] + [研究目的] + [期望深度]

示例：
✅ "2025年人工智能在医疗诊断领域的商业投资机会深度分析"
✅ "短视频电商平台的盈利模式和创业可行性研究"
✅ "新能源汽车行业的技术发展趋势和市场前景预测"
```

**避免的查询类型：**
```
❌ "人工智能"（过于宽泛）
❌ "如何赚钱"（缺乏具体性）
❌ "区块链好不好"（问题不明确）
```

### 2. 明确研究意图

**商业分析类查询：**
- "2025年最具投资价值的新兴行业机会分析"
- "直播带货的盈利模式和市场竞争策略研究"
- "SaaS行业的商业模式创新和发展机会"

**技术研究类查询：**
- "生成式AI技术的最新突破和应用前景分析"
- "量子计算在金融科技中的技术可行性研究"
- "边缘计算技术的发展趋势和产业化路径"

**市场调研类查询：**
- "中国新消费品牌的市场格局和竞争态势"
- "碳中和政策下的绿色科技市场机会分析"
- "数字化转型中的企业服务市场研究"

### 3. 指定目标受众

在查询中明确目标受众：
- "面向投资者的..."
- "适合创业者的..."
- "企业决策者需要了解的..."
- "技术人员关注的..."

## 📊 工作流程详解

### 第1步：意图分析
- 系统分析用户查询的语义和关键词
- 识别研究类型、时间维度、深度需求
- 确定目标受众和分析框架
- 生成核心研究问题

### 第2步：研究规划
- 基于意图分析结果制定研究计划
- 选择适合的分析方法和框架
- 确定信息收集策略
- 明确研究目标和成功指标

### 第3步：查询生成
- 根据研究类型生成专业化搜索查询
- 考虑不同信息源的特点
- 优化关键词和搜索策略
- 确保查询的针对性和时效性

### 第4步：信息搜索
- 使用生成的精准查询执行搜索
- 收集多维度、高质量的信息
- 验证信息的可靠性和时效性
- 整理和预处理搜索结果

### 第5步：深度分析
- 采用针对性的分析框架
- 提供符合目标受众需求的洞察
- 进行量化分析和趋势预测
- 识别关键成功因素和风险点

### 第6步：报告生成
- 生成符合用户期望的专业报告
- 确保内容深度和实用性
- 提供具体的行动建议
- 包含量化数据和预测分析

## 🎯 意图识别示例

### 示例1：商业机会分析
**用户查询**："今年最能赚钱的互联网项目有哪些"

**意图识别结果**：
- 研究类型：商业机会分析
- 时间重点：2025年现状分析
- 深度需求：深度专业分析
- 目标受众：创业者/投资者
- 核心问题：盈利模式、市场机会、投资回报

**生成的搜索查询**：
1. "2025年互联网创业项目盈利模式分析"
2. "2025年最具投资价值的互联网赛道"
3. "互联网项目2025年投资回报数据统计"

### 示例2：技术趋势研究
**用户查询**："人工智能在自动驾驶领域的技术发展现状"

**意图识别结果**：
- 研究类型：技术趋势研究
- 时间重点：2025年现状分析
- 深度需求：深度专业分析
- 目标受众：技术人员/研究人员
- 核心问题：技术突破、应用现状、发展趋势

**生成的搜索查询**：
1. "2025年自动驾驶AI技术最新突破"
2. "人工智能自动驾驶应用场景分析"
3. "自动驾驶AI技术发展路线图2025"

## 🔧 高级配置

### 环境变量优化
```env
# 提高分析质量的配置
MODEL_NAME=gpt-4o  # 使用更强大的模型
MODEL_TEMPERATURE=0.05  # 降低随机性
MODEL_MAX_TOKENS=6000  # 增加输出长度
SEARCH_MAX_RESULTS=6  # 增加搜索结果数量
```

### 自定义意图类型
可以通过修改代码添加新的意图识别类型：
```python
# 在intent_analyzer函数中添加新的研究类型
"新研究类型": {
    "keywords": ["关键词1", "关键词2"],
    "analysis_focus": "分析重点",
    "output_style": "输出特点"
}
```

## 📈 效果对比

### 传统方式 vs 意图分析方式

**传统方式**：
- 使用通用搜索查询
- 标准化分析框架
- 一刀切的报告结构
- 缺乏针对性

**意图分析方式**：
- 精准的专业化搜索
- 定制化分析框架
- 符合用户期望的报告
- 高度针对性和实用性

通过意图分析功能，研究报告的质量和针对性将显著提升！
